import { z } from "zod";
import dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();
import dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();

const envSchema = z.object({
  PORT: z.string().default("3001"),
  MONGODB_URI: z.string().min(1, "MONGODB_URI is required"),
  JWT_SECRET: z.string().min(1, "JWT_SECRET is required"),
  ADMIN_JWT_SECRET: z.string().min(1, "ADMIN_JWT_SECRET is required"),
  NODE_ENV: z.string().default("development"),

  // Optional environment variables for filling service
  EMAIL_USER: z.string().optional(),
  EMAIL_PASS: z.string().optional(),
  REDIS_URL: z.string().optional(),
  TWILIO_SID: z.string().optional(),
  TWILIO_AUTH_TOKEN: z.string().optional(),
  TWILIO_PHONE_NUMBER: z.string().optional(),
  AWS_ACCESS_KEY_ID: z.string().optional(),
  AWS_SECRET_ACCESS_KEY: z.string().optional(),
  AWS_REGION: z.string().optional(),
  AWS_S3_BUCKET_NAME: z.string().optional(),
});

export const env = envSchema.parse(process.env);
